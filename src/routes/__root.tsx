import { AppProvider } from "@shopify/polaris";
import translations from "@shopify/polaris/locales/en.json";
import type { QueryClient } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { HeadContent, Outlet, Scripts, createRootRouteWithContext } from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import * as React from "react";

import polarisCss from "@shopify/polaris/build/esm/styles.css?url";
import { DefaultCatchBoundary } from "~/components/DefaultCatchBoundary";
import { NotFound } from "~/components/NotFound";
import { checkShopParam } from "~/middleware/shopCheck";
import { useGetShop } from "~/shared/common/common.queries";
import rootStore from "~/stores/root";
import appCss from "~/styles/app.css?url";
import { seo } from "~/utils/seo";

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient;
}>()({
  beforeLoad: async (ctx) => {
    const { shop, admin, host } = checkShopParam(ctx.search, ctx.location.pathname);
    ctx.context.queryClient.setQueryData(["shopify-api-key"], {
      shop,
      admin,
      host
    });
    // Gọi getShop từ store để fetch shop data mỗi khi vào trang web
    if (shop) {
      await rootStore.getState().getShop(shop);
    }
  },
  head: () => ({
    meta: [
      {
        charSet: "utf-8"
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1"
      },
      ...seo({
        title: "Orichi Hide Pricing",
        description: `Orichi Hide Pricing is a Shopify app that allows you to hide pricing on your products. `
      }),
      {
        name: "shopify-api-key",
        content: import.meta.env.VITE_SHOPIFY_APP_CLIENT_ID || ""
      }
    ],
    links: [
      { rel: "stylesheet", href: polarisCss },
      { rel: "stylesheet", href: appCss },
      {
        rel: "apple-touch-icon",
        sizes: "180x180",
        href: "/apple-touch-icon.png"
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "32x32",
        href: "/favicon-32x32.png"
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "16x16",
        href: "/favicon-16x16.png"
      },
      { rel: "manifest", href: "/site.webmanifest", color: "#fffff" },
      { rel: "icon", href: "/favicon.ico" }
    ],
    scripts: [
      {
        children: `window.shopify = {
						config: {
							disabledFeatures: ["auto-redirect"]
						}
					}`,
        type: "text/javascript"
      },
      {
        src: "https://cdn.shopify.com/shopifycloud/app-bridge.js"
      }
    ]
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    );
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent
});

function RootComponent() {
  const { queryClient } = Route.useRouteContext();
  const shopData = queryClient.getQueryData(["shopify-api-key"]) as {
    shop?: string;
    admin?: string;
    host?: string;
  };

  // Sử dụng useGetShop để sync với React Query
  useGetShop(shopData?.shop);

  return (
    <RootDocument>
      <AppProvider i18n={translations}>
        <Outlet />
      </AppProvider>
    </RootDocument>
  );
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html lang='en' suppressHydrationWarning>
      <head>
        <HeadContent />
      </head>
      <body suppressHydrationWarning>
        {children}
        <TanStackRouterDevtools position='bottom-right' />
        <ReactQueryDevtools buttonPosition='bottom-left' />
        <Scripts />
      </body>
    </html>
  );
}
