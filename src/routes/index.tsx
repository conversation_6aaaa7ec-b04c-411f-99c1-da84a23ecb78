import { Page } from "@shopify/polaris";
import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";

import { useNavigate } from "~/hooks/use-navigate";

//random name
const randomName = Math.random().toString(36).substring(2, 15);

const getUser = async () => {
  return {
    name: randomName
  };
};
export const Route = createFileRoute("/")({
  component: Home,

  loader: async ({ context }) => {
    const data = await getUser();
    context.queryClient.setQueryData(["user"], data);
    return data;
  },

  pendingComponent: () => {
    return <div>Loading...</div>;
  }
});

function Home() {
  const data = Route.useLoaderData();
  const [enabled, setEnabled] = useState(false);

  const { navigate, navigateAndRemoveSearch } = useNavigate();
  console.log("env", import.meta.env.VITE_BACKEND_END_POINT);

  return <Page>{data.name}</Page>;
}
