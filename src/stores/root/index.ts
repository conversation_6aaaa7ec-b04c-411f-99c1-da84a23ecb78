/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosInstance } from "axios";
import { create } from "zustand";

import CommonApi from "~/apis/common.api";
import { ResponseModel } from "~/models/api/response.model";
import { ShopDataResponse, ShopInfoDto } from "~/models/common/shop-data.model";

type State = {
  authAxios: AxiosInstance;
  shop: ShopDataResponse | null;
  isAdmin: boolean;
  isLoading: boolean;
  isEnableTheme: boolean;
};

type Action = {
  addInterceptorAxios: (token: string) => void;
  getShop: (shopDomain: string) => Promise<ResponseModel<ShopInfoDto>>;
  setIsAdmin: (isAdmin: boolean) => void;
  setIsEnableTheme: (isEnableTheme: boolean) => void;
};

const rootStore = create<State & Action>((set, get) => ({
  isLoading: false,
  authAxios: axios.create({
    baseURL: import.meta.env.VITE_BACKEND_END_POINT,
    timeout: 30 * 3600
  }),
  shop: null,
  isAdmin: false,
  isEnableTheme: true,

  addInterceptorAxios: (jwt: string) => {
    const instance = get().authAxios;
    instance.interceptors.request.use((config: any) => {
      // add token to request headers
      config.headers.Authorization = `Bearer ${jwt}`;
      return config;
    });

    instance.interceptors.response.use(
      (response: any) => {
        return response;
      },
      (error: any) => {
        return Promise.reject(error);
      }
    );
    set({ authAxios: instance });
  },

  getShop: async (domain: string) => {
    set({ isLoading: true });
    const response = await CommonApi.GetShop(domain);
    set({
      isLoading: false,
      shop: response.result?.shop
    });
    if (response?.result) get().addInterceptorAxios(response.result.token);
    return response;
  },

  setIsAdmin: (isAdmin: boolean) => {
    set({ isAdmin: isAdmin });
  },
  setIsEnableTheme: (isEnableTheme: boolean) => {
    set({ isEnableTheme: isEnableTheme });
  }
}));

export default rootStore;
